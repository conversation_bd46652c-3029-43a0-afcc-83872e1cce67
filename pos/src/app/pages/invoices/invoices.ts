import { ChangeDetectorRef, Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { ButtonModule } from 'primeng/button';
import { OrderService } from "src/app/services/order.service";
import { CommonService } from "src/app/services/common";
import { PrintService } from "src/app/services/print.service";

import { Order } from "src/app/models";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent, ButtonModule]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    selectedOrder: Order | null = null;

    constructor(
        private orderService: OrderService,
        private commonService: CommonService,
        private cdr: ChangeDetectorRef,
        private printService: PrintService
    ) {

    }
    ionViewDidEnter(){
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.cartItems = []
        this.loadOrders();
    }
    loadOrders() {
        this.orderService.getOrders().then((orders: any) => {
            this.invoices = orders?.data;
        }).catch((error: any) => {
            console.error('Error fetching orders:', error);
            this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to load orders. Please try again.' });
        });
    }
    onRowClick(ev: any) {
        this.getRowData(ev.rowData?.order_id)
    }

    async getRowData(id: string) {
        try {
            this.isLoading = true;
            const response: any = await this.orderService.getOrderDetails(id);

            if (response?.success && response?.data) {
                this.selectedOrder = response.data;
                this.cartItems = response.data.items || [];
            } else {
                this.selectedOrder = null;
                this.cartItems = [];
                this.commonService.toast({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load order details'
                });
            }
        } catch (error) {
            console.error('Error fetching order details:', error);
            this.selectedOrder = null;
            this.cartItems = [];
            this.commonService.toast({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load order details'
            });
        } finally {
            this.isLoading = false;
            this.cdr.detectChanges();
        }
    }

    async printInvoice() {
        if (!this.selectedOrder || this.cartItems.length === 0) {
            this.commonService.toast({
                severity: 'warn',
                summary: 'Warning',
                detail: 'Please select an order to print invoice copy'
            });
            return;
        }

        try {
            await this.printService.printOrder(this.selectedOrder, true);
            this.commonService.toast({
                severity: 'success',
                summary: 'Success',
                detail: 'Invoice copy sent to printer'
            });
        } catch (error) {
            console.error('Error printing invoice:', error);
            this.commonService.toast({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to print invoice. Please try again.'
            });
        }
    }
}