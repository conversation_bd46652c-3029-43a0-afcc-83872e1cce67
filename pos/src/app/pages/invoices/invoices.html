<ion-content [fullscreen]="true">
   <app-header></app-header>
   <!-- Mobile/Tablet Grand Total -->
   <div class="w-full bg-blue-50 p-3 rounded-md mb-3 lg:hidden" *ngIf="selectedOrder">
     <div class="flex items-center justify-center gap-2">
       <span class="font-semibold">Invoice Total:</span>
       <span class="text-lg font-bold">₹{{selectedOrder.total_amount.toFixed(2)}}</span>
     </div>
   </div>
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 p-3">
    <div class="col-span-1 lg:col-span-2 mt-3">
        <app-table
            [tableData]="invoices"
            [tableColumns]="invoicesColumns"
            [dataKey]="'order_id'"
            (onRowClick)="onRowClick($event)">
        </app-table>
    </div>
    
    <div class="col-span-1 lg:col-span-1 p-3">
      <button class="w-full" pButton severity="info" [outlined]="true" label="Print Invoice Copy"
              (click)="printInvoice()" [disabled]="!selectedOrder || isLoading"></button>
      <app-billing [noDataTitle]="''" [noDataMessage]="'Select Order to view details'" [showTable]="true" [cartItems]="cartItems || []"></app-billing>
    </div>
</div>
</ion-content>
